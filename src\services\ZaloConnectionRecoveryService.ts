import { <PERSON><PERSON> } from 'zca-js';
import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { zaloMultiAccountManager } from './ZaloMultiAccountManager';
import crypto from 'crypto';

export interface RecoveryAttempt {
  accountId: string;
  attemptNumber: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

export interface RecoveryStats {
  totalAttempts: number;
  successfulRecoveries: number;
  failedRecoveries: number;
  lastRecoveryTime?: Date;
  accountsRecovered: number;
  accountsFailed: number;
}

export class ZaloConnectionRecoveryService {
  private recoveryAttempts: Map<string, RecoveryAttempt[]> = new Map();
  private recoveryInterval: NodeJS.Timeout | null = null;
  private isRecoveryRunning = false;
  private maxRetryAttempts = 3;
  private retryDelayMs = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.startRecoveryMonitoring();
  }

  /**
   * Start automatic recovery monitoring
   */
  private startRecoveryMonitoring(): void {
    // Run recovery check every 10 minutes
    this.recoveryInterval = setInterval(async () => {
      if (!this.isRecoveryRunning) {
        await this.performRecoveryCheck();
      }
    }, 10 * 60 * 1000);

    logger.info('Connection recovery monitoring started');
  }

  /**
   * Perform recovery check for all accounts
   */
  async performRecoveryCheck(): Promise<void> {
    if (this.isRecoveryRunning) {
      logger.debug('Recovery already running, skipping');
      return;
    }

    this.isRecoveryRunning = true;

    try {
      logger.info('Starting connection recovery check');

      // Get accounts that need recovery
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .in('status', ['error', 'inactive'])
        .not('error_data', 'is', null);

      if (error) {
        throw new Error(`Failed to fetch accounts for recovery: ${error.message}`);
      }

      if (!accounts || accounts.length === 0) {
        logger.debug('No accounts need recovery');
        return;
      }

      logger.info(`Found ${accounts.length} accounts that may need recovery`);

      // Attempt recovery for each account
      for (const account of accounts) {
        try {
          await this.attemptAccountRecovery(account);
        } catch (error: any) {
          logger.error('Failed to recover account', {
            accountId: account.id,
            error: error.message
          });
        }
      }

      logger.info('Recovery check completed');

    } catch (error: any) {
      logger.error('Recovery check failed', { error: error.message });
    } finally {
      this.isRecoveryRunning = false;
    }
  }

  /**
   * Attempt to recover a specific account
   */
  async attemptAccountRecovery(account: any): Promise<boolean> {
    const accountId = account.id;
    const attempts = this.recoveryAttempts.get(accountId) || [];

    // Check if we've exceeded max retry attempts
    const recentAttempts = attempts.filter(
      attempt => Date.now() - attempt.timestamp.getTime() < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    if (recentAttempts.length >= this.maxRetryAttempts) {
      logger.debug('Max retry attempts exceeded for account', { accountId });
      return false;
    }

    // Check if enough time has passed since last attempt
    const lastAttempt = attempts[attempts.length - 1];
    if (lastAttempt && Date.now() - lastAttempt.timestamp.getTime() < this.retryDelayMs) {
      logger.debug('Not enough time passed since last recovery attempt', { accountId });
      return false;
    }

    logger.info('Attempting to recover account', {
      accountId,
      attemptNumber: recentAttempts.length + 1
    });

    const recoveryAttempt: RecoveryAttempt = {
      accountId,
      attemptNumber: recentAttempts.length + 1,
      timestamp: new Date(),
      success: false
    };

    try {
      // Check if account has valid auth data
      if (!account.auth_data?.cookies) {
        throw new Error('No valid auth data found for account');
      }

      // Attempt to create new Zalo instance
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: true
      });

      const api = await zalo.login({
        imei: account.auth_data.imei || crypto.randomUUID(),
        cookie: account.auth_data.cookies,
        userAgent: account.auth_data.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      });

      // Test the connection
      await api.fetchAccountInfo();

      // Update account status in database
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          status: 'active',
          last_activity_at: new Date().toISOString(),
          error_data: null // Clear error data
        })
        .eq('id', accountId);

      // Add to multi-account manager
      const accountSession = {
        accountId: account.id,
        tenantId: account.tenant_id,
        zaloUserId: account.zalo_user_id,
        api: api,
        status: 'active' as const,
        lastActivity: new Date(),
        connectionAttempts: 0,
        maxRetries: 3,
        chatwootInboxId: account.chatwoot_inbox_id
      };

      // Add to manager (this will replace any existing session)
      (zaloMultiAccountManager as any).accountSessions.set(accountId, accountSession);

      // Start API listener
      api.listener.start();

      recoveryAttempt.success = true;

      logger.info('Account recovery successful', {
        accountId,
        zaloUserId: account.zalo_user_id
      });

      return true;

    } catch (error: any) {
      recoveryAttempt.error = error.message;

      logger.warn('Account recovery failed', {
        accountId,
        error: error.message,
        attemptNumber: recoveryAttempt.attemptNumber
      });

      // Update error data in database
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          error_data: {
            ...account.error_data,
            last_recovery_attempt: new Date().toISOString(),
            recovery_attempts: recentAttempts.length + 1,
            last_recovery_error: error.message
          }
        })
        .eq('id', accountId);

      return false;

    } finally {
      // Store recovery attempt
      attempts.push(recoveryAttempt);
      this.recoveryAttempts.set(accountId, attempts);
    }
  }

  /**
   * Force recovery attempt for specific account
   */
  async forceRecoveryAttempt(accountId: string): Promise<boolean> {
    try {
      logger.info('Force recovery attempt for account', { accountId });

      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('id', accountId)
        .single();

      if (error || !account) {
        throw new Error(`Account not found: ${accountId}`);
      }

      return await this.attemptAccountRecovery(account);

    } catch (error: any) {
      logger.error('Force recovery attempt failed', {
        accountId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Get recovery statistics
   */
  getRecoveryStats(): RecoveryStats {
    let totalAttempts = 0;
    let successfulRecoveries = 0;
    let failedRecoveries = 0;
    let lastRecoveryTime: Date | undefined;
    const accountsWithAttempts = new Set<string>();
    const accountsRecovered = new Set<string>();

    for (const [accountId, attempts] of this.recoveryAttempts.entries()) {
      accountsWithAttempts.add(accountId);
      
      for (const attempt of attempts) {
        totalAttempts++;
        
        if (attempt.success) {
          successfulRecoveries++;
          accountsRecovered.add(accountId);
          
          if (!lastRecoveryTime || attempt.timestamp > lastRecoveryTime) {
            lastRecoveryTime = attempt.timestamp;
          }
        } else {
          failedRecoveries++;
        }
      }
    }

    return {
      totalAttempts,
      successfulRecoveries,
      failedRecoveries,
      lastRecoveryTime,
      accountsRecovered: accountsRecovered.size,
      accountsFailed: accountsWithAttempts.size - accountsRecovered.size
    };
  }

  /**
   * Clear recovery history for account
   */
  clearRecoveryHistory(accountId: string): void {
    this.recoveryAttempts.delete(accountId);
    logger.info('Recovery history cleared for account', { accountId });
  }

  /**
   * Clear all recovery history
   */
  clearAllRecoveryHistory(): void {
    this.recoveryAttempts.clear();
    logger.info('All recovery history cleared');
  }

  /**
   * Get recovery attempts for specific account
   */
  getAccountRecoveryHistory(accountId: string): RecoveryAttempt[] {
    return this.recoveryAttempts.get(accountId) || [];
  }

  /**
   * Stop recovery monitoring
   */
  stop(): void {
    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
    }
    logger.info('Connection recovery monitoring stopped');
  }

  /**
   * Check if recovery is currently running
   */
  isRunning(): boolean {
    return this.isRecoveryRunning;
  }

  /**
   * Update recovery configuration
   */
  updateConfig(config: {
    maxRetryAttempts?: number;
    retryDelayMs?: number;
  }): void {
    if (config.maxRetryAttempts !== undefined) {
      this.maxRetryAttempts = config.maxRetryAttempts;
    }
    if (config.retryDelayMs !== undefined) {
      this.retryDelayMs = config.retryDelayMs;
    }

    logger.info('Recovery configuration updated', {
      maxRetryAttempts: this.maxRetryAttempts,
      retryDelayMs: this.retryDelayMs
    });
  }
}

// Export singleton instance
export const zaloConnectionRecoveryService = new ZaloConnectionRecoveryService();
