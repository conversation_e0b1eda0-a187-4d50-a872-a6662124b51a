# 🚀 Zalo Multi-Account System - <PERSON><PERSON> thống Đăng nhập <PERSON>ề<PERSON> Tài khoản Zalo

## 📋 Tổng quan

Hệ thống Zalo Multi-Account đã được tối ưu hóa hoàn toàn với các tính năng:

- ✅ **QR Code chính xác từ zca-js library** - Kh<PERSON>ng còn mock QR
- ✅ **Multi-account management** - Quản lý nhiều tài khoản đồng thời
- ✅ **Session isolation** - Mỗi tài khoản có session riêng biệt
- ✅ **Connection pooling** - Tối ưu hóa kết nối
- ✅ **Auto-recovery** - Tự động khôi phục kết nối khi lỗi
- ✅ **Health monitoring** - Giám sát sức khỏe hệ thống
- ✅ **QR Storage optimization** - Lưu trữ QR code trên Supabase
- ✅ **Graceful shutdown** - T<PERSON><PERSON> hệ thống an toàn

## 🏗️ Kiến trú<PERSON> thống

### Core Services

1. **ZaloMultiAccountManager** - Qu<PERSON><PERSON> lý multiple Zalo accounts
2. **QRStorageService** - Quản lý QR code storage và cleanup
3. **ZaloConnectionRecoveryService** - Auto-recovery và reconnection
4. **ZaloAuthService** - Authentication với zca-js integration

### API Endpoints

- `/api/zalo-multi-account/create-qr` - Tạo QR login mới
- `/api/zalo-multi-account/qr-status/:sessionId` - Kiểm tra trạng thái QR
- `/api/zalo-multi-account/accounts` - Danh sách tài khoản
- `/api/zalo-multi-account/disconnect/:accountId` - Ngắt kết nối
- `/api/zalo-multi-account/stats` - Thống kê hệ thống
- `/api/zalo-multi-account/health` - Health check

## 🚀 Quick Start

### 1. Cài đặt Dependencies

```bash
# Đã có sẵn trong package.json
npm install
# hoặc
yarn install
```

### 2. Cấu hình Environment

```env
# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Default tenant (optional)
DEFAULT_TENANT_ID=your-default-tenant-id
```

### 3. Khởi động Server

```bash
npm run dev
```

### 4. Test Hệ thống

```bash
# Test toàn bộ hệ thống
npm run test-multi-account
```

## 📱 Sử dụng API

### Tạo QR Login

```bash
curl -X POST http://localhost:3000/api/zalo-multi-account/create-qr \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: your-tenant-id" \
  -d '{
    "chatwoot_inbox_id": 123,
    "expires_in_minutes": 10,
    "user_agent": "MyApp/1.0"
  }'
```

Response:
```json
{
  "success": true,
  "data": {
    "account_id": "uuid",
    "session_id": "uuid", 
    "qr_code_url": "https://storage-url/qr.png",
    "expires_at": "2024-01-01T10:00:00Z",
    "instructions": {
      "step1": "Display the QR code to user",
      "step2": "User scans QR code with Zalo app",
      "step3": "Poll QR status using session_id",
      "step4": "Account will be automatically activated"
    }
  }
}
```

### Kiểm tra Trạng thái QR

```bash
curl http://localhost:3000/api/zalo-multi-account/qr-status/SESSION_ID \
  -H "X-Tenant-ID: your-tenant-id"
```

Response:
```json
{
  "success": true,
  "data": {
    "session_id": "uuid",
    "status": "pending|scanned|confirmed|expired|failed",
    "account_id": "uuid",
    "zalo_user_id": "*********",
    "message": "Status description"
  }
}
```

### Lấy Danh sách Tài khoản

```bash
curl http://localhost:3000/api/zalo-multi-account/accounts \
  -H "X-Tenant-ID: your-tenant-id"
```

### Thống kê Hệ thống

```bash
curl http://localhost:3000/api/zalo-multi-account/stats \
  -H "X-Tenant-ID: your-tenant-id"
```

## 🔧 Tính năng Nâng cao

### Auto-Recovery

Hệ thống tự động:
- Kiểm tra kết nối mỗi 10 phút
- Khôi phục tài khoản bị lỗi
- Retry với exponential backoff
- Cleanup expired sessions

### Health Monitoring

- Real-time connection status
- Memory usage tracking
- Performance metrics
- Error rate monitoring

### QR Storage Management

- Upload QR codes to Supabase Storage
- Automatic cleanup of expired QRs
- Storage usage statistics
- CDN-ready public URLs

## 🛠️ Development

### Cấu trúc Code

```
src/
├── services/
│   ├── ZaloMultiAccountManager.ts    # Core multi-account logic
│   ├── QRStorageService.ts           # QR storage management
│   ├── ZaloConnectionRecoveryService.ts # Auto-recovery
│   └── ZaloAuthService.ts            # Updated with zca-js
├── routes/
│   └── zalo-multi-account.ts         # API endpoints
└── scripts/
    └── test-multi-account-system.ts  # Test script
```

### Testing

```bash
# Test core system
npm run test-multi-account

# Test specific components
npm run test-qr

# Manual API testing
curl http://localhost:3000/api/zalo-multi-account/health
```

### Debugging

```bash
# Enable debug logging
DEBUG=zalo:* npm run dev

# Check logs
tail -f logs/app.log
```

## 📊 Monitoring

### Health Check

```bash
curl http://localhost:3000/api/zalo-multi-account/health
```

### System Stats

```bash
curl http://localhost:3000/api/zalo-multi-account/stats \
  -H "X-Tenant-ID: your-tenant-id"
```

### Recovery Stats

Xem trong logs hoặc qua API stats để theo dõi:
- Số lần recovery thành công
- Tài khoản được khôi phục
- Error rates

## 🔒 Security

- **Tenant Isolation** - Mỗi tenant chỉ thấy data của mình
- **RLS Policies** - Row Level Security trên Supabase
- **Secure Storage** - QR codes có expiration time
- **Rate Limiting** - Giới hạn số request QR generation

## 🚨 Troubleshooting

### QR Code không tạo được

1. Kiểm tra zca-js library version
2. Xem logs để debug callback events
3. Verify Supabase storage permissions

### Tài khoản không kết nối được

1. Check auth_data trong database
2. Verify cookies còn valid
3. Run force recovery: `POST /api/zalo-multi-account/recovery/ACCOUNT_ID`

### Memory leaks

1. Monitor với `GET /api/zalo-multi-account/health`
2. Check session cleanup intervals
3. Restart services nếu cần

## 📈 Performance

- **Connection Pooling** - Tái sử dụng connections
- **Lazy Loading** - Chỉ load khi cần
- **Background Processing** - Recovery chạy background
- **Efficient Storage** - QR cleanup tự động

## 🔄 Migration từ hệ thống cũ

1. Backup existing data
2. Update database schema
3. Migrate existing accounts
4. Test thoroughly
5. Deploy gradually

## 📝 Changelog

### v2.0.0 - Multi-Account System
- ✅ Replaced mock QR with real zca-js integration
- ✅ Added multi-account management
- ✅ Implemented auto-recovery
- ✅ Added QR storage optimization
- ✅ Enhanced monitoring and health checks

---

## 🎯 Next Steps

1. **Start server**: `npm run dev`
2. **Test system**: `npm run test-multi-account`
3. **Create QR**: `POST /api/zalo-multi-account/create-qr`
4. **Monitor**: `GET /api/zalo-multi-account/health`

**Happy coding! 🚀**
