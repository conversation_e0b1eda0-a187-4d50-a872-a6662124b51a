import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export interface QRStorageOptions {
  sessionId: string;
  tenantId: string;
  accountId: string;
  expiresAt: Date;
  metadata?: Record<string, any>;
}

export interface QRStorageResult {
  publicUrl: string;
  storagePath: string;
  uploadedAt: Date;
}

export class QRStorageService {
  private readonly bucketName = 'qr-codes';
  private readonly localQRDir = path.join(process.cwd(), 'qr_codes');

  constructor() {
    this.ensureLocalDirectory();
  }

  /**
   * Ensure local QR directory exists
   */
  private ensureLocalDirectory(): void {
    if (!fs.existsSync(this.localQRDir)) {
      fs.mkdirSync(this.localQRDir, { recursive: true });
      logger.info('Created local QR directory', { path: this.localQRDir });
    }
  }

  /**
   * Upload QR code to Supabase Storage
   */
  async uploadQRCode(qrPath: string, options: QRStorageOptions): Promise<QRStorageResult> {
    try {
      logger.info('Uploading QR code to storage', {
        sessionId: options.sessionId,
        qrPath
      });

      // Verify file exists
      if (!fs.existsSync(qrPath)) {
        throw new Error(`QR code file not found: ${qrPath}`);
      }

      // Read file
      const qrBuffer = fs.readFileSync(qrPath);
      const fileSize = qrBuffer.length;

      // Generate storage path with timestamp for uniqueness
      const timestamp = Date.now();
      const storagePath = `${options.tenantId}/${options.sessionId}_${timestamp}.png`;

      // Upload to Supabase Storage
      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .upload(storagePath, qrBuffer, {
          contentType: 'image/png',
          cacheControl: '3600', // Cache for 1 hour
          upsert: true
        });

      if (error) {
        throw new Error(`Failed to upload QR to storage: ${error.message}`);
      }

      // Get public URL
      const { data: publicUrlData } = supabaseAdmin.storage
        .from(this.bucketName)
        .getPublicUrl(storagePath);

      const result: QRStorageResult = {
        publicUrl: publicUrlData.publicUrl,
        storagePath,
        uploadedAt: new Date()
      };

      // Store QR record in database
      await this.createQRRecord(options, result, fileSize);

      // Clean up local file
      this.cleanupLocalFile(qrPath);

      logger.info('QR code uploaded successfully', {
        sessionId: options.sessionId,
        publicUrl: result.publicUrl,
        fileSize
      });

      return result;

    } catch (error: any) {
      logger.error('Failed to upload QR code', {
        sessionId: options.sessionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get QR code public URL by session ID
   */
  async getQRUrl(sessionId: string): Promise<string | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('qr_codes')
        .select('qr_image_url')
        .eq('session_id', sessionId)
        .single();

      if (error || !data) {
        return null;
      }

      return data.qr_image_url;

    } catch (error: any) {
      logger.error('Failed to get QR URL', { sessionId, error: error.message });
      return null;
    }
  }

  /**
   * Delete QR code from storage and database
   */
  async deleteQRCode(sessionId: string): Promise<void> {
    try {
      // Get QR record from database
      const { data: qrRecord } = await supabaseAdmin
        .from('qr_codes')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (qrRecord) {
        // Delete from storage
        if (qrRecord.storage_path) {
          const { error: storageError } = await supabaseAdmin.storage
            .from(this.bucketName)
            .remove([qrRecord.storage_path]);

          if (storageError) {
            logger.warn('Failed to delete QR from storage', {
              sessionId,
              storagePath: qrRecord.storage_path,
              error: storageError.message
            });
          }
        }

        // Delete from database
        await supabaseAdmin
          .from('qr_codes')
          .delete()
          .eq('session_id', sessionId);

        logger.info('QR code deleted successfully', { sessionId });
      }

    } catch (error: any) {
      logger.error('Failed to delete QR code', { sessionId, error: error.message });
    }
  }

  /**
   * Cleanup expired QR codes
   */
  async cleanupExpiredQRCodes(): Promise<number> {
    try {
      logger.info('Starting QR codes cleanup');

      // Get expired QR codes
      const { data: expiredQRs, error } = await supabaseAdmin
        .from('qr_codes')
        .select('*')
        .lt('expires_at', new Date().toISOString());

      if (error) {
        throw new Error(`Failed to fetch expired QR codes: ${error.message}`);
      }

      if (!expiredQRs || expiredQRs.length === 0) {
        logger.info('No expired QR codes found');
        return 0;
      }

      let cleanedCount = 0;

      // Delete each expired QR code
      for (const qr of expiredQRs) {
        try {
          // Delete from storage
          if (qr.storage_path) {
            await supabaseAdmin.storage
              .from(this.bucketName)
              .remove([qr.storage_path]);
          }

          // Delete from database
          await supabaseAdmin
            .from('qr_codes')
            .delete()
            .eq('id', qr.id);

          cleanedCount++;

        } catch (cleanupError: any) {
          logger.warn('Failed to cleanup individual QR code', {
            qrId: qr.id,
            sessionId: qr.session_id,
            error: cleanupError.message
          });
        }
      }

      logger.info('QR codes cleanup completed', {
        totalExpired: expiredQRs.length,
        cleanedCount
      });

      return cleanedCount;

    } catch (error: any) {
      logger.error('Failed to cleanup expired QR codes', { error: error.message });
      return 0;
    }
  }

  /**
   * Get QR storage statistics
   */
  async getStorageStats(): Promise<{
    totalQRs: number;
    activeQRs: number;
    expiredQRs: number;
    totalStorageSize: number;
  }> {
    try {
      // Get total count
      const { count: totalQRs } = await supabaseAdmin
        .from('qr_codes')
        .select('*', { count: 'exact', head: true });

      // Get active count
      const { count: activeQRs } = await supabaseAdmin
        .from('qr_codes')
        .select('*', { count: 'exact', head: true })
        .gte('expires_at', new Date().toISOString());

      // Get expired count
      const { count: expiredQRs } = await supabaseAdmin
        .from('qr_codes')
        .select('*', { count: 'exact', head: true })
        .lt('expires_at', new Date().toISOString());

      // Get total storage size
      const { data: qrRecords } = await supabaseAdmin
        .from('qr_codes')
        .select('file_size');

      const totalStorageSize = qrRecords?.reduce((sum, record) => {
        return sum + (record.file_size || 0);
      }, 0) || 0;

      return {
        totalQRs: totalQRs || 0,
        activeQRs: activeQRs || 0,
        expiredQRs: expiredQRs || 0,
        totalStorageSize
      };

    } catch (error: any) {
      logger.error('Failed to get storage stats', { error: error.message });
      return {
        totalQRs: 0,
        activeQRs: 0,
        expiredQRs: 0,
        totalStorageSize: 0
      };
    }
  }

  /**
   * Create QR record in database
   */
  private async createQRRecord(
    options: QRStorageOptions,
    result: QRStorageResult,
    fileSize: number
  ): Promise<void> {
    try {
      await supabaseAdmin
        .from('qr_codes')
        .insert({
          tenant_id: options.tenantId,
          session_id: options.sessionId,
          account_id: options.accountId,
          qr_image_url: result.publicUrl,
          storage_path: result.storagePath,
          file_size: fileSize,
          status: 'pending',
          expires_at: options.expiresAt.toISOString(),
          metadata: options.metadata || {},
          created_at: new Date().toISOString()
        });

    } catch (error: any) {
      logger.error('Failed to create QR record', {
        sessionId: options.sessionId,
        error: error.message
      });
      // Don't throw here as the upload was successful
    }
  }

  /**
   * Clean up local file
   */
  private cleanupLocalFile(qrPath: string): void {
    try {
      if (fs.existsSync(qrPath)) {
        fs.unlinkSync(qrPath);
        logger.debug('Local QR file cleaned up', { qrPath });
      }
    } catch (error: any) {
      logger.warn('Failed to cleanup local QR file', {
        qrPath,
        error: error.message
      });
    }
  }

  /**
   * Generate local QR path for session
   */
  getLocalQRPath(sessionId: string): string {
    return path.join(this.localQRDir, `${sessionId}.png`);
  }
}

// Export singleton instance
export const qrStorageService = new QRStorageService();
