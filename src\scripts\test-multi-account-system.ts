#!/usr/bin/env ts-node

import * as dotenv from 'dotenv';
dotenv.config();

import { zaloMultiAccountManager } from '../services/ZaloMultiAccountManager';
import { qrStorageService } from '../services/QRStorageService';
import { zaloConnectionRecoveryService } from '../services/ZaloConnectionRecoveryService';
import { logger } from '../utils/logger';

async function testMultiAccountSystem() {
  try {
    logger.info('🧪 Starting Multi-Account System Test');

    // Test 1: Initialize Multi-Account Manager
    logger.info('📋 Test 1: Initializing Multi-Account Manager');
    await zaloMultiAccountManager.initialize();
    logger.info('✅ Multi-Account Manager initialized successfully');

    // Test 2: Get initial stats
    logger.info('📋 Test 2: Getting initial statistics');
    const initialStats = zaloMultiAccountManager.getStats();
    logger.info('📊 Initial Stats:', initialStats);

    // Test 3: Create QR login session
    logger.info('📋 Test 3: Creating QR login session');
    const testTenantId = 'test-tenant-' + Date.now();
    
    try {
      const qrResult = await zaloMultiAccountManager.createQRLogin(testTenantId, {
        expiresInMinutes: 5,
        userAgent: 'Test-Agent/1.0'
      });
      
      logger.info('✅ QR login session created:', {
        accountId: qrResult.accountId,
        sessionId: qrResult.sessionId,
        qrCodeUrl: qrResult.qrCodeUrl,
        expiresAt: qrResult.expiresAt
      });

      // Test 4: Check QR status
      logger.info('📋 Test 4: Checking QR status');
      const qrStatus = await zaloMultiAccountManager.getQRStatus(qrResult.sessionId);
      logger.info('📊 QR Status:', qrStatus);

      // Test 5: Get updated stats
      logger.info('📋 Test 5: Getting updated statistics');
      const updatedStats = zaloMultiAccountManager.getStats();
      logger.info('📊 Updated Stats:', updatedStats);

    } catch (qrError: any) {
      logger.warn('⚠️ QR creation test failed (expected in test environment):', qrError.message);
    }

    // Test 6: Test QR Storage Service
    logger.info('📋 Test 6: Testing QR Storage Service');
    const storageStats = await qrStorageService.getStorageStats();
    logger.info('📊 Storage Stats:', storageStats);

    // Test 7: Test Connection Recovery Service
    logger.info('📋 Test 7: Testing Connection Recovery Service');
    const recoveryStats = zaloConnectionRecoveryService.getRecoveryStats();
    logger.info('📊 Recovery Stats:', recoveryStats);

    // Test 8: Test accounts by tenant
    logger.info('📋 Test 8: Getting accounts by tenant');
    const tenantAccounts = zaloMultiAccountManager.getAccountsByTenant(testTenantId);
    logger.info('📊 Tenant Accounts:', tenantAccounts.length);

    // Test 9: Cleanup expired QR codes
    logger.info('📋 Test 9: Testing QR cleanup');
    const cleanedCount = await qrStorageService.cleanupExpiredQRCodes();
    logger.info('🧹 Cleaned QR codes:', cleanedCount);

    // Test 10: Force recovery check (should be safe with no accounts)
    logger.info('📋 Test 10: Testing recovery check');
    await zaloConnectionRecoveryService.performRecoveryCheck();
    logger.info('✅ Recovery check completed');

    logger.info('🎉 All tests completed successfully!');

    // Display final summary
    logger.info('📊 Final System Summary:');
    const finalStats = zaloMultiAccountManager.getStats();
    const finalStorageStats = await qrStorageService.getStorageStats();
    const finalRecoveryStats = zaloConnectionRecoveryService.getRecoveryStats();

    console.log('\n=== SYSTEM SUMMARY ===');
    console.log('Multi-Account Manager:');
    console.log(`  - Total Accounts: ${finalStats.totalAccounts}`);
    console.log(`  - Active Accounts: ${finalStats.activeAccounts}`);
    console.log(`  - QR Sessions: ${finalStats.totalQRSessions}`);
    console.log(`  - Pending QRs: ${finalStats.pendingQRSessions}`);

    console.log('\nQR Storage:');
    console.log(`  - Total QR Codes: ${finalStorageStats.totalQRs}`);
    console.log(`  - Active QR Codes: ${finalStorageStats.activeQRs}`);
    console.log(`  - Expired QR Codes: ${finalStorageStats.expiredQRs}`);
    console.log(`  - Storage Size: ${Math.round(finalStorageStats.totalStorageSize / 1024)} KB`);

    console.log('\nConnection Recovery:');
    console.log(`  - Total Attempts: ${finalRecoveryStats.totalAttempts}`);
    console.log(`  - Successful Recoveries: ${finalRecoveryStats.successfulRecoveries}`);
    console.log(`  - Failed Recoveries: ${finalRecoveryStats.failedRecoveries}`);
    console.log(`  - Accounts Recovered: ${finalRecoveryStats.accountsRecovered}`);

    console.log('\n✅ Multi-Account System is working correctly!');

  } catch (error: any) {
    logger.error('❌ Test failed:', error);
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

async function testAPIEndpoints() {
  try {
    logger.info('🌐 Testing API Endpoints (requires server to be running)');

    const baseUrl = 'http://localhost:3000';
    const testTenantId = 'test-tenant-' + Date.now();

    // Test health endpoint
    logger.info('📋 Testing health endpoint');
    const healthResponse = await fetch(`${baseUrl}/api/zalo-multi-account/health`);
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      logger.info('✅ Health endpoint working:', healthData.data.status);
    } else {
      logger.warn('⚠️ Health endpoint not accessible (server may not be running)');
    }

    // Test stats endpoint
    logger.info('📋 Testing stats endpoint');
    const statsResponse = await fetch(`${baseUrl}/api/zalo-multi-account/stats`, {
      headers: {
        'X-Tenant-ID': testTenantId
      }
    });
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      logger.info('✅ Stats endpoint working:', statsData.data.accounts);
    } else {
      logger.warn('⚠️ Stats endpoint not accessible');
    }

    logger.info('🎉 API endpoint tests completed!');

  } catch (error: any) {
    logger.warn('⚠️ API endpoint tests failed (server may not be running):', error.message);
  }
}

async function main() {
  console.log('🚀 Starting Zalo Multi-Account System Tests\n');

  // Test core system
  await testMultiAccountSystem();

  console.log('\n' + '='.repeat(50));

  // Test API endpoints (optional)
  await testAPIEndpoints();

  console.log('\n🎉 All tests completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Start the server: npm run dev');
  console.log('2. Test QR creation: POST /api/zalo-multi-account/create-qr');
  console.log('3. Monitor accounts: GET /api/zalo-multi-account/accounts');
  console.log('4. Check system health: GET /api/zalo-multi-account/health');

  // Cleanup
  await zaloMultiAccountManager.shutdown();
  zaloConnectionRecoveryService.stop();

  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  logger.error('Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

// Run tests
main().catch((error) => {
  logger.error('Main function failed:', error);
  process.exit(1);
});
