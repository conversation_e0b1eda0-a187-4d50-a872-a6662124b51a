import { Router, Request, Response } from 'express';
import { zaloMultiAccountManager } from '../services/ZaloMultiAccountManager';
import { qrStorageService } from '../services/QRStorageService';
import { validateTenant } from '../middleware/validateTenant';
import { asyncHandler } from '../middleware/asyncHandler';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = Router();

// POST /api/zalo-multi-account/create-qr - Tạo QR login mới
router.post('/create-qr', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const {
    chatwoot_inbox_id,
    expires_in_minutes = 10,
    user_agent
  } = req.body;

  try {
    logger.info('Creating QR login for multi-account', {
      tenantId,
      chatwootInboxId: chatwoot_inbox_id,
      expiresInMinutes: expires_in_minutes,
      requestId: req.id
    });

    const result = await zaloMultiAccountManager.createQRLogin(tenantId, {
      chatwootInboxId: chatwoot_inbox_id,
      expiresInMinutes: expires_in_minutes,
      userAgent: user_agent || req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'QR login created successfully',
      data: {
        account_id: result.accountId,
        session_id: result.sessionId,
        qr_code_url: result.qrCodeUrl,
        expires_at: result.expiresAt,
        chatwoot_inbox_id: chatwoot_inbox_id,
        tenant_id: tenantId,
        instructions: {
          step1: 'Display the QR code to user using qr_code_url',
          step2: 'User scans QR code with Zalo app',
          step3: 'Poll QR status using session_id',
          step4: 'Account will be automatically activated when login completes'
        }
      }
    });

  } catch (error: any) {
    logger.error('Failed to create QR login', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to create QR login: ${error.message}`,
      500,
      'QR_CREATION_FAILED'
    );
  }
}));

// GET /api/zalo-multi-account/qr-status/:sessionId - Kiểm tra trạng thái QR
router.get('/qr-status/:sessionId', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const status = await zaloMultiAccountManager.getQRStatus(sessionId);

    res.json({
      success: true,
      message: 'QR status retrieved successfully',
      data: {
        session_id: sessionId,
        status: status.status,
        account_id: status.accountId,
        zalo_user_id: status.zaloUserId,
        message: status.message,
        tenant_id: tenantId,
        checked_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get QR status', {
      sessionId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to get QR status: ${error.message}`,
      500,
      'QR_STATUS_CHECK_FAILED'
    );
  }
}));

// GET /api/zalo-multi-account/accounts - Lấy danh sách tài khoản
router.get('/accounts', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    const accounts = zaloMultiAccountManager.getAccountsByTenant(tenantId);
    const stats = zaloMultiAccountManager.getStats();

    res.json({
      success: true,
      message: 'Accounts retrieved successfully',
      data: {
        tenant_id: tenantId,
        accounts: accounts.map(account => ({
          account_id: account.accountId,
          zalo_user_id: account.zaloUserId,
          status: account.status,
          last_activity: account.lastActivity.toISOString(),
          connection_attempts: account.connectionAttempts,
          chatwoot_inbox_id: account.chatwootInboxId,
          is_connected: account.status === 'active' && account.api !== null
        })),
        stats: {
          total_accounts: stats.totalAccounts,
          active_accounts: stats.activeAccounts,
          inactive_accounts: stats.inactiveAccounts,
          error_accounts: stats.errorAccounts,
          connecting_accounts: stats.connectingAccounts
        },
        retrieved_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get accounts', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to get accounts: ${error.message}`,
      500,
      'ACCOUNTS_RETRIEVAL_FAILED'
    );
  }
}));

// POST /api/zalo-multi-account/disconnect/:accountId - Ngắt kết nối tài khoản
router.post('/disconnect/:accountId', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  try {
    // Verify account belongs to tenant
    const session = zaloMultiAccountManager.getAccountSession(accountId);
    if (!session || session.tenantId !== tenantId) {
      throw new AppError('Account not found or access denied', 404, 'ACCOUNT_NOT_FOUND');
    }

    await zaloMultiAccountManager.disconnectAccount(accountId);

    res.json({
      success: true,
      message: 'Account disconnected successfully',
      data: {
        account_id: accountId,
        tenant_id: tenantId,
        disconnected_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to disconnect account', {
      accountId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to disconnect account: ${error.message}`,
      500,
      'ACCOUNT_DISCONNECT_FAILED'
    );
  }
}));

// GET /api/zalo-multi-account/stats - Lấy thống kê tổng quan
router.get('/stats', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    const managerStats = zaloMultiAccountManager.getStats();
    const storageStats = await qrStorageService.getStorageStats();

    res.json({
      success: true,
      message: 'Statistics retrieved successfully',
      data: {
        tenant_id: tenantId,
        accounts: {
          total: managerStats.totalAccounts,
          active: managerStats.activeAccounts,
          inactive: managerStats.inactiveAccounts,
          error: managerStats.errorAccounts,
          connecting: managerStats.connectingAccounts
        },
        qr_sessions: {
          total: managerStats.totalQRSessions,
          pending: managerStats.pendingQRSessions
        },
        storage: {
          total_qr_codes: storageStats.totalQRs,
          active_qr_codes: storageStats.activeQRs,
          expired_qr_codes: storageStats.expiredQRs,
          total_storage_size: storageStats.totalStorageSize,
          storage_size_mb: Math.round(storageStats.totalStorageSize / 1024 / 1024 * 100) / 100
        },
        retrieved_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get stats', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to get statistics: ${error.message}`,
      500,
      'STATS_RETRIEVAL_FAILED'
    );
  }
}));

// POST /api/zalo-multi-account/cleanup - Dọn dẹp dữ liệu cũ
router.post('/cleanup', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    logger.info('Starting cleanup process', { tenantId, requestId: req.id });

    const cleanedQRs = await qrStorageService.cleanupExpiredQRCodes();

    res.json({
      success: true,
      message: 'Cleanup completed successfully',
      data: {
        tenant_id: tenantId,
        cleaned_qr_codes: cleanedQRs,
        cleanup_completed_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to perform cleanup', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to perform cleanup: ${error.message}`,
      500,
      'CLEANUP_FAILED'
    );
  }
}));

// GET /api/zalo-multi-account/health - Kiểm tra sức khỏe hệ thống
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = zaloMultiAccountManager.getStats();
    const storageStats = await qrStorageService.getStorageStats();

    const isHealthy = stats.totalAccounts > 0 || stats.totalQRSessions > 0;

    res.json({
      success: true,
      message: isHealthy ? 'System is healthy' : 'System is idle',
      data: {
        status: isHealthy ? 'healthy' : 'idle',
        uptime: process.uptime(),
        memory_usage: process.memoryUsage(),
        accounts_summary: {
          total: stats.totalAccounts,
          active: stats.activeAccounts,
          error: stats.errorAccounts
        },
        qr_sessions_summary: {
          total: stats.totalQRSessions,
          pending: stats.pendingQRSessions
        },
        storage_summary: {
          total_qr_codes: storageStats.totalQRs,
          expired_qr_codes: storageStats.expiredQRs
        },
        checked_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Health check failed', { error: error.message });

    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: error.message,
      checked_at: new Date().toISOString()
    });
  }
}));

export default router;
