import { <PERSON>alo } from 'zca-js';
import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

export interface ZaloAccountSession {
  accountId: string;
  tenantId: string;
  zaloUserId: string;
  api: any;
  status: 'active' | 'inactive' | 'error' | 'connecting';
  lastActivity: Date;
  connectionAttempts: number;
  maxRetries: number;
  qrSessionId?: string;
  chatwootInboxId?: number;
}

export interface QRLoginSession {
  sessionId: string;
  accountId: string;
  tenantId: string;
  qrPath: string;
  expiresAt: Date;
  status: 'pending' | 'scanned' | 'confirmed' | 'expired' | 'failed';
  loginPromise?: Promise<any>;
}

export interface MultiAccountStats {
  totalAccounts: number;
  activeAccounts: number;
  inactiveAccounts: number;
  errorAccounts: number;
  connectingAccounts: number;
  totalQRSessions: number;
  pendingQRSessions: number;
}

export class ZaloMultiAccountManager {
  private accountSessions: Map<string, ZaloAccountSession> = new Map();
  private qrSessions: Map<string, QRLoginSession> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startHealthMonitoring();
    this.startCleanupProcess();
  }

  /**
   * Initialize manager and recover existing sessions from database
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing ZaloMultiAccountManager');

      // Load active accounts from database
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active');

      if (error) {
        logger.error('Failed to load accounts from database', { error: error.message });
        return;
      }

      if (!accounts || accounts.length === 0) {
        logger.info('No active accounts found in database');
        return;
      }

      logger.info(`Found ${accounts.length} active accounts, attempting to recover sessions`);

      // Attempt to recover each account session
      for (const account of accounts) {
        try {
          await this.recoverAccountSession(account);
        } catch (error: any) {
          logger.error('Failed to recover account session', {
            accountId: account.id,
            error: error.message
          });
        }
      }

      logger.info('ZaloMultiAccountManager initialization completed', {
        totalSessions: this.accountSessions.size,
        activeSessions: Array.from(this.accountSessions.values()).filter(s => s.status === 'active').length
      });

    } catch (error: any) {
      logger.error('Failed to initialize ZaloMultiAccountManager', { error: error.message });
    }
  }

  /**
   * Create new QR login session
   */
  async createQRLogin(tenantId: string, options: {
    chatwootInboxId?: number;
    expiresInMinutes?: number;
    userAgent?: string;
  } = {}): Promise<{
    accountId: string;
    sessionId: string;
    qrCodeUrl: string;
    expiresAt: string;
  }> {
    try {
      const sessionId = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + (options.expiresInMinutes || 10) * 60 * 1000);
      const qrPath = path.join(process.cwd(), 'qr_codes', `${sessionId}.png`);

      // Ensure QR codes directory exists
      const qrDir = path.dirname(qrPath);
      if (!fs.existsSync(qrDir)) {
        fs.mkdirSync(qrDir, { recursive: true });
      }

      // Create account record in database
      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .insert({
          tenant_id: tenantId,
          zalo_user_id: `pending_${sessionId}`,
          status: 'qr_pending',
          auth_data: {
            user_agent: options.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            imei: '',
            cookies: {}
          },
          qr_data: {
            qr_session_id: sessionId,
            qr_status: 'pending',
            qr_expires_at: expiresAt.toISOString(),
            qr_code_path: qrPath
          },
          chatwoot_inbox_id: options.chatwootInboxId,
          login_method: 'qr'
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create account record: ${error.message}`);
      }

      // Generate QR code using zca-js
      const qrResult = await this.generateQRCode(qrPath, sessionId, options.userAgent);

      // Store QR session
      const qrSession: QRLoginSession = {
        sessionId,
        accountId: account.id,
        tenantId,
        qrPath,
        expiresAt,
        status: 'pending',
        loginPromise: qrResult.loginPromise
      };

      this.qrSessions.set(sessionId, qrSession);

      // Upload QR to storage and get public URL
      const qrCodeUrl = await this.uploadQRToStorage(qrPath, sessionId);

      // Update account with QR image URL
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          qr_data: {
            ...account.qr_data,
            qr_image_url: qrCodeUrl
          }
        })
        .eq('id', account.id);

      logger.info('QR login session created', {
        sessionId,
        accountId: account.id,
        tenantId,
        expiresAt: expiresAt.toISOString()
      });

      return {
        accountId: account.id,
        sessionId,
        qrCodeUrl,
        expiresAt: expiresAt.toISOString()
      };

    } catch (error: any) {
      logger.error('Failed to create QR login session', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get QR login status
   */
  async getQRStatus(sessionId: string): Promise<{
    status: string;
    accountId?: string;
    zaloUserId?: string;
    message?: string;
  }> {
    try {
      const qrSession = this.qrSessions.get(sessionId);
      if (!qrSession) {
        return { status: 'not_found', message: 'QR session not found' };
      }

      // Check if expired
      if (new Date() > qrSession.expiresAt) {
        qrSession.status = 'expired';
        this.qrSessions.delete(sessionId);
        return { status: 'expired', message: 'QR code has expired' };
      }

      // Get latest status from database
      const { data: account } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('id', qrSession.accountId)
        .single();

      if (account) {
        const qrStatus = account.qr_data?.qr_status || 'pending';
        return {
          status: qrStatus,
          accountId: account.id,
          zaloUserId: account.zalo_user_id !== `pending_${sessionId}` ? account.zalo_user_id : undefined,
          message: this.getStatusMessage(qrStatus)
        };
      }

      return { status: 'pending', accountId: qrSession.accountId };

    } catch (error: any) {
      logger.error('Failed to get QR status', { sessionId, error: error.message });
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Get account session by ID
   */
  getAccountSession(accountId: string): ZaloAccountSession | undefined {
    return this.accountSessions.get(accountId);
  }

  /**
   * Get all account sessions for a tenant
   */
  getAccountsByTenant(tenantId: string): ZaloAccountSession[] {
    return Array.from(this.accountSessions.values())
      .filter(session => session.tenantId === tenantId);
  }

  /**
   * Get manager statistics
   */
  getStats(): MultiAccountStats {
    const sessions = Array.from(this.accountSessions.values());
    const qrSessions = Array.from(this.qrSessions.values());

    return {
      totalAccounts: sessions.length,
      activeAccounts: sessions.filter(s => s.status === 'active').length,
      inactiveAccounts: sessions.filter(s => s.status === 'inactive').length,
      errorAccounts: sessions.filter(s => s.status === 'error').length,
      connectingAccounts: sessions.filter(s => s.status === 'connecting').length,
      totalQRSessions: qrSessions.length,
      pendingQRSessions: qrSessions.filter(s => s.status === 'pending').length
    };
  }

  /**
   * Disconnect account session
   */
  async disconnectAccount(accountId: string): Promise<void> {
    try {
      const session = this.accountSessions.get(accountId);
      if (session) {
        // Stop API listener if exists
        if (session.api && session.api.listener) {
          session.api.listener.stop();
        }

        // Remove from memory
        this.accountSessions.delete(accountId);

        // Update database status
        await supabaseAdmin
          .from('zalo_accounts')
          .update({ status: 'inactive' })
          .eq('id', accountId);

        logger.info('Account session disconnected', { accountId });
      }
    } catch (error: any) {
      logger.error('Failed to disconnect account', { accountId, error: error.message });
    }
  }

  /**
   * Generate QR code using zca-js
   */
  private async generateQRCode(qrPath: string, sessionId: string, userAgent?: string): Promise<{
    loginPromise: Promise<any>;
    qrGenerated: boolean;
  }> {
    try {
      logger.info('Generating QR code with zca-js', { sessionId, qrPath });

      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: true
      });

      let qrGenerated = false;
      let resolveQR: (value: any) => void;
      let rejectQR: (reason: any) => void;

      const qrPromise = new Promise((resolve, reject) => {
        resolveQR = resolve;
        rejectQR = reject;
      });

      // Start QR login with callback
      const loginPromise = zalo.loginQR({
        userAgent: userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        qrPath: qrPath
      }, (event) => {
        logger.info('QR Event received', { type: event.type, sessionId });

        switch (event.type) {
          case 0: // QRCodeGenerated
            logger.info('QR code generated', { sessionId });
            qrGenerated = true;

            if (event.actions?.saveToFile) {
              event.actions.saveToFile(qrPath).then(() => {
                logger.info('QR code saved to file', { qrPath, sessionId });
                resolveQR({ qrPath });
              }).catch((error) => {
                logger.error('Failed to save QR code', { error: error.message, sessionId });
                rejectQR(error);
              });
            } else {
              resolveQR({ qrPath });
            }
            break;

          case 1: // QRCodeExpired
            logger.warn('QR code expired', { sessionId });
            this.updateQRStatus(sessionId, 'expired');
            rejectQR(new Error('QR code expired'));
            break;

          case 2: // QRCodeScanned
            logger.info('QR code scanned', { sessionId, userInfo: event.data });
            this.updateQRStatus(sessionId, 'scanned');
            break;

          case 3: // QRCodeDeclined
            logger.warn('QR code declined', { sessionId });
            this.updateQRStatus(sessionId, 'failed');
            rejectQR(new Error('QR code declined'));
            break;

          case 4: // GotLoginInfo
            logger.info('Login info received', { sessionId });
            this.handleLoginCompletion(sessionId, event.data);
            break;
        }
      });

      // Wait for QR generation with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('QR generation timeout')), 30000);
      });

      try {
        await Promise.race([qrPromise, timeoutPromise]);
      } catch (error: any) {
        logger.warn('QR generation timeout or error', { sessionId, error: error.message });
        // Continue anyway, QR might still be generated
      }

      return { loginPromise, qrGenerated };

    } catch (error: any) {
      logger.error('Failed to generate QR code', { sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Upload QR code to Supabase Storage
   */
  private async uploadQRToStorage(qrPath: string, sessionId: string): Promise<string> {
    try {
      const qrBuffer = fs.readFileSync(qrPath);
      const storagePath = `qr-codes/${sessionId}.png`;

      const { data, error } = await supabaseAdmin.storage
        .from('qr-codes')
        .upload(storagePath, qrBuffer, {
          contentType: 'image/png',
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw new Error(`Failed to upload QR to storage: ${error.message}`);
      }

      const { data: publicUrlData } = supabaseAdmin.storage
        .from('qr-codes')
        .getPublicUrl(storagePath);

      // Clean up local file
      try {
        fs.unlinkSync(qrPath);
      } catch (cleanupError) {
        logger.warn('Failed to cleanup local QR file', { qrPath });
      }

      return publicUrlData.publicUrl;

    } catch (error: any) {
      logger.error('Failed to upload QR to storage', { sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Update QR status in database
   */
  private async updateQRStatus(sessionId: string, status: string): Promise<void> {
    try {
      const qrSession = this.qrSessions.get(sessionId);
      if (qrSession) {
        qrSession.status = status as any;

        await supabaseAdmin
          .from('zalo_accounts')
          .update({
            qr_data: {
              qr_session_id: sessionId,
              qr_status: status,
              updated_at: new Date().toISOString()
            }
          })
          .eq('id', qrSession.accountId);
      }
    } catch (error: any) {
      logger.error('Failed to update QR status', { sessionId, status, error: error.message });
    }
  }

  /**
   * Handle login completion
   */
  private async handleLoginCompletion(sessionId: string, loginData: any): Promise<void> {
    try {
      const qrSession = this.qrSessions.get(sessionId);
      if (!qrSession) {
        logger.warn('QR session not found for login completion', { sessionId });
        return;
      }

      logger.info('Handling login completion', { sessionId, accountId: qrSession.accountId });

      // Wait for login promise to resolve
      const api = await qrSession.loginPromise;
      if (!api) {
        throw new Error('Failed to get API instance from login');
      }

      // Get user info
      const userInfo = await api.fetchAccountInfo();
      const cookies = api.getCookie();

      // Update account in database
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          zalo_user_id: userInfo.uid || userInfo.userId,
          zalo_display_name: userInfo.name || userInfo.displayName,
          zalo_avatar_url: userInfo.avatar,
          status: 'active',
          auth_data: {
            cookies: cookies,
            imei: loginData.imei || crypto.randomUUID(),
            user_agent: loginData.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          qr_data: {
            qr_session_id: sessionId,
            qr_status: 'confirmed',
            confirmed_at: new Date().toISOString()
          },
          last_login_at: new Date().toISOString(),
          last_activity_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        })
        .eq('id', qrSession.accountId);

      // Create account session
      const accountSession: ZaloAccountSession = {
        accountId: qrSession.accountId,
        tenantId: qrSession.tenantId,
        zaloUserId: userInfo.uid || userInfo.userId,
        api: api,
        status: 'active',
        lastActivity: new Date(),
        connectionAttempts: 0,
        maxRetries: 3,
        chatwootInboxId: undefined // Will be set later if needed
      };

      this.accountSessions.set(qrSession.accountId, accountSession);

      // Start API listener
      api.listener.start();

      // Clean up QR session
      this.qrSessions.delete(sessionId);

      logger.info('Login completion handled successfully', {
        sessionId,
        accountId: qrSession.accountId,
        zaloUserId: userInfo.uid || userInfo.userId
      });

    } catch (error: any) {
      logger.error('Failed to handle login completion', { sessionId, error: error.message });

      // Update QR session status to failed
      await this.updateQRStatus(sessionId, 'failed');
    }
  }

  /**
   * Recover account session from database
   */
  private async recoverAccountSession(account: any): Promise<void> {
    try {
      logger.info('Recovering account session', { accountId: account.id });

      if (!account.auth_data?.cookies) {
        logger.warn('No cookies found for account, cannot recover', { accountId: account.id });
        return;
      }

      // Create Zalo instance with existing credentials
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: true
      });

      const api = await zalo.login({
        imei: account.auth_data.imei || crypto.randomUUID(),
        cookie: account.auth_data.cookies,
        userAgent: account.auth_data.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      });

      // Create account session
      const accountSession: ZaloAccountSession = {
        accountId: account.id,
        tenantId: account.tenant_id,
        zaloUserId: account.zalo_user_id,
        api: api,
        status: 'active',
        lastActivity: new Date(),
        connectionAttempts: 0,
        maxRetries: 3,
        chatwootInboxId: account.chatwoot_inbox_id
      };

      this.accountSessions.set(account.id, accountSession);

      // Start API listener
      api.listener.start();

      logger.info('Account session recovered successfully', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id
      });

    } catch (error: any) {
      logger.error('Failed to recover account session', {
        accountId: account.id,
        error: error.message
      });

      // Update account status to error
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          status: 'error',
          error_data: {
            error_message: error.message,
            error_code: 'RECOVERY_FAILED',
            retry_count: 0,
            next_retry_at: new Date(Date.now() + 5 * 60 * 1000).toISOString()
          }
        })
        .eq('id', account.id);
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error: any) {
        logger.error('Health check failed', { error: error.message });
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Start cleanup process
   */
  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performCleanup();
      } catch (error: any) {
        logger.error('Cleanup failed', { error: error.message });
      }
    }, 15 * 60 * 1000); // Every 15 minutes
  }

  /**
   * Perform health check on all sessions
   */
  private async performHealthCheck(): Promise<void> {
    logger.debug('Performing health check on account sessions');

    for (const [accountId, session] of this.accountSessions.entries()) {
      try {
        if (session.status === 'active' && session.api) {
          // Try to ping the API to check if it's still alive
          await session.api.keepAlive();
          session.lastActivity = new Date();
          session.connectionAttempts = 0;
        }
      } catch (error: any) {
        logger.warn('Health check failed for account', {
          accountId,
          error: error.message
        });

        session.connectionAttempts++;
        if (session.connectionAttempts >= session.maxRetries) {
          session.status = 'error';
          logger.error('Account marked as error after max retries', { accountId });
        }
      }
    }
  }

  /**
   * Perform cleanup of expired sessions and data
   */
  private async performCleanup(): Promise<void> {
    logger.debug('Performing cleanup');

    // Clean up expired QR sessions
    const now = new Date();
    for (const [sessionId, qrSession] of this.qrSessions.entries()) {
      if (now > qrSession.expiresAt) {
        logger.info('Cleaning up expired QR session', { sessionId });
        this.qrSessions.delete(sessionId);

        // Update database
        await this.updateQRStatus(sessionId, 'expired');
      }
    }

    // Clean up error sessions that haven't been active for too long
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    for (const [accountId, session] of this.accountSessions.entries()) {
      if (session.status === 'error' && session.lastActivity < cutoffTime) {
        logger.info('Cleaning up old error session', { accountId });
        this.accountSessions.delete(accountId);
      }
    }
  }

  /**
   * Get status message for QR status
   */
  private getStatusMessage(status: string): string {
    switch (status) {
      case 'pending':
        return 'Waiting for user to scan QR code';
      case 'scanned':
        return 'QR code scanned, waiting for confirmation';
      case 'confirmed':
        return 'Login successful';
      case 'expired':
        return 'QR code has expired';
      case 'failed':
        return 'Login failed or declined';
      default:
        return 'Unknown status';
    }
  }

  /**
   * Shutdown manager and cleanup resources
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down ZaloMultiAccountManager');

    // Clear intervals
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Disconnect all sessions
    for (const accountId of this.accountSessions.keys()) {
      await this.disconnectAccount(accountId);
    }

    // Clear maps
    this.accountSessions.clear();
    this.qrSessions.clear();

    logger.info('ZaloMultiAccountManager shutdown completed');
  }
}

// Export singleton instance
export const zaloMultiAccountManager = new ZaloMultiAccountManager();
